# Terms of Service

**Effective Date**: January 1, 2025

**Last Updated**: January 1, 2025

## 1. Agreement to Terms

By accessing or using **AIBuild** (the "Service"), a comprehensive NextJS boilerplate platform for building AI-powered SaaS applications, you ("User," "you," or "your") agree to be legally bound by these Terms of Service ("Terms"). If you disagree with any part of these terms, you may not access or use our Service.

AIBuild is operated by aibuild.dev ("Company," "we," "us," or "our").

## 2. Description of Service

AIBuild provides developers with:

- Pre-built NextJS templates optimized for AI SaaS applications
- Integrated development tools and infrastructure components
- Documentation and support resources
- Access to our development platform and associated services

## 3. User Accounts and Eligibility

### 3.1 Account Registration

- You must create an account to access certain features
- You must provide accurate, current, and complete information
- You must be at least 18 years old or have parental consent
- One person or legal entity may maintain only one account

### 3.2 Account Security

- You are solely responsible for your account credentials
- You must immediately notify us of any unauthorized access
- You are liable for all activities under your account
- We reserve the right to suspend accounts showing suspicious activity

### 3.3 Account Termination

- You may terminate your account at any time
- We may suspend or terminate accounts for Terms violations
- Upon termination, your right to use the Service ceases immediately

## 4. Intellectual Property Rights

### 4.1 Our Content

- AIBuild platform, templates, code, and documentation are owned by aibuild.dev
- All content is protected by copyright, trademark, and other intellectual property laws
- We grant you a limited, non-exclusive, non-transferable license to use our Service

### 4.2 Your Content

- You retain ownership of your custom implementations and modifications
- You grant us a license to host, store, and display your content as necessary to provide the Service
- You represent that you have all necessary rights to your content

### 4.3 Restrictions

- You may not reverse engineer, decompile, or disassemble our Service
- You may not create derivative works based on our proprietary code
- You may not remove or modify any proprietary notices

## 5. Acceptable Use Policy

### 5.1 Permitted Uses

- Building and deploying AI SaaS applications using our templates
- Customizing and modifying templates for your specific needs
- Using our documentation and support resources

### 5.2 Prohibited Activities

- Violating any applicable laws or regulations
- Infringing on intellectual property rights
- Distributing malware or harmful code
- Attempting to gain unauthorized access to our systems
- Reselling or redistributing our templates without authorization
- Using the Service to compete directly with AIBuild
- Engaging in any activity that disrupts or interferes with the Service

## 6. Payment Terms and Billing

### 6.1 Pricing

- Current pricing is available on our website
- Prices may change with 30 days' notice to active subscribers
- All fees are non-refundable except as required by law

### 6.2 Payment Processing

- Payments are processed through secure third-party providers
- You authorize us to charge your selected payment method
- Failed payments may result in service suspension

### 6.3 Taxes

- You are responsible for all applicable taxes
- Prices are exclusive of taxes unless otherwise stated

## 7. Privacy and Data Protection

We collect and process personal information as described in our [Privacy Policy](/privacy-policy). By using our Service, you consent to such collection and processing in accordance with our Privacy Policy.

## 8. Service Availability and Modifications

### 8.1 Service Availability

- We strive for high availability but do not guarantee uninterrupted service
- Scheduled maintenance will be announced in advance when possible
- We are not liable for service interruptions beyond our reasonable control

### 8.2 Service Modifications

- We may modify, update, or discontinue features at any time
- Material changes will be communicated to users in advance
- Continued use after changes constitutes acceptance

## 9. Disclaimers and Warranties

### 9.1 Service Provided "As Is"

THE SERVICE IS PROVIDED "AS IS" AND "AS AVAILABLE" WITHOUT WARRANTIES OF ANY KIND, WHETHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.

### 9.2 No Guarantee of Results

- We do not guarantee that the Service will meet your specific requirements
- We do not warrant that the Service will be error-free or secure
- You use the Service at your own risk

## 10. Limitation of Liability

TO THE MAXIMUM EXTENT PERMITTED BY LAW, AIBUILD.DEV SHALL NOT BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES, INCLUDING BUT NOT LIMITED TO LOSS OF PROFITS, DATA, OR USE, ARISING FROM YOUR USE OF THE SERVICE, EVEN IF WE HAVE BEEN ADVISED OF THE POSSIBILITY OF SUCH DAMAGES.

OUR TOTAL LIABILITY SHALL NOT EXCEED THE AMOUNT YOU PAID FOR THE SERVICE IN THE 12 MONTHS PRECEDING THE CLAIM.

## 11. Indemnification

You agree to indemnify, defend, and hold harmless aibuild.dev, its affiliates, officers, directors, employees, and agents from any claims, damages, losses, liabilities, and expenses (including reasonable attorneys' fees) arising from:

- Your use of the Service
- Your violation of these Terms
- Your violation of any third-party rights
- Your content or applications built using our Service

## 12. Dispute Resolution

### 12.1 Governing Law

These Terms are governed by the laws of [Jurisdiction], without regard to conflict of law principles.

### 12.2 Dispute Resolution Process

- Initial disputes should be addressed through our support channels
- Unresolved disputes will be settled through binding arbitration
- Arbitration will be conducted under the rules of [Arbitration Organization]
- You waive any right to participate in class action lawsuits

## 13. General Provisions

### 13.1 Entire Agreement

These Terms constitute the entire agreement between you and aibuild.dev regarding the Service.

### 13.2 Severability

If any provision is found unenforceable, the remaining provisions will remain in full force and effect.

### 13.3 No Waiver

Our failure to enforce any provision does not constitute a waiver of that provision.

### 13.4 Assignment

You may not assign these Terms without our written consent. We may assign these Terms at any time.

## 14. Updates to Terms

We reserve the right to modify these Terms at any time. Material changes will be communicated through:

- Email notification to registered users
- Prominent notice on our website
- In-app notifications

Your continued use of the Service after changes become effective constitutes acceptance of the new Terms.

## 15. Contact Information

For questions about these Terms, please contact us: [<EMAIL>](mailto:<EMAIL>)

---

**By using AIBuild, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.**
