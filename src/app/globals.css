@import "tailwindcss";
@import "./theme.css";

@plugin "tailwindcss-animate";

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@keyframes gradient-x {
  0%,
  100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes marquee-scroll {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@layer utilities {
  .animate-gradient-x {
    animation: gradient-x 3s ease infinite;
    background-size: 200% 200%;
  }

  /* seamless scrolling Marquee */
  .marquee-container {
    overflow: hidden;
    position: relative;
    mask-image: linear-gradient(
      to right,
      transparent,
      black 10%,
      black 90%,
      transparent
    );
  }

  .marquee-wrapper {
    display: flex;
    width: fit-content;
    animation: marquee-scroll 20s linear infinite;
  }

  .marquee-track {
    display: flex;
    gap: 3rem;
    min-width: fit-content;
  }

  .marquee-track:not(:first-child) {
    margin-left: 3rem;
  }

  .marquee-item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    width: 120px;
  }

  .marquee-logo {
    height: 1.5rem;
    width: auto;
    max-width: 100%;
    object-fit: contain;
    transition: all 0.2s ease;
  }

  @media (min-width: 768px) {
    .marquee-logo {
      height: 2.5rem;
    }

    .marquee-track {
      gap: 4rem;
    }

    .marquee-track:not(:first-child) {
      margin-left: 4rem;
    }

    .marquee-item {
      width: 140px;
    }
  }

  /* SVG Logo dark mode */
  .svg-logo {
    filter: brightness(0) saturate(100%) invert(0%) sepia(100%) saturate(0%)
      hue-rotate(0deg) brightness(100%) contrast(100%);
  }

  .dark .svg-logo {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%)
      hue-rotate(0deg) brightness(100%) contrast(100%);
  }
}

.container {
  @apply mx-auto max-w-7xl px-4 md:px-8;
}

input,
select,
textarea {
  @apply border-border outline-ring/50 bg-background;
}

button {
  @apply cursor-pointer border-border outline-ring/50;
}

pre {
  @apply px-4;
}
