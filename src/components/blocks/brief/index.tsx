import { Card, CardContent } from "@/components/ui/card";
import { Section as SectionType } from "@/types/blocks/section";
import { X, Check } from "lucide-react";

interface ComparisonItem {
  title: string;
  subtitle: string;
  theme: "positive" | "negative";
  items: string[];
}

interface BriefSection extends SectionType {
  comparison?: {
    left: ComparisonItem;
    right: ComparisonItem;
  };
}

export default function Brief({ section }: { section: BriefSection }) {
  if (section.disabled) {
    return null;
  }

  const leftComparison = section.comparison?.left;
  const rightComparison = section.comparison?.right;

  if (!leftComparison || !rightComparison) {
    return null;
  }

  return (
    <section
      id={section.name}
      className="text-center py-16 space-y-8"
      data-theme="dark"
    >
      {/* Background Image */}
      <div className="mb-8 flex justify-center">
        <img
          src="/imgs/features/code-vs-build.png"
          alt="Traditional coding vs AI-powered building comparison"
          className="max-w-4xl w-full h-auto rounded-lg shadow-lg"
        />
      </div>

      <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
        {/* Left Card (Traditional Code - Negative theme) */}
        <Card className="py-0 rounded-lg border border-red-300 bg-red-950/10">
          <CardContent className="p-6 space-y-4">
            <h3 className="text-2xl font-bold flex items-center justify-between text-red-300">
              {leftComparison.title}
              <div className="size-8 rounded-full bg-red-300 flex items-center justify-center">
                <X className="size-5 text-red-900" strokeWidth={3} />
              </div>
            </h3>
            <p className="text-sm text-red-300 mb-4">
              {leftComparison.subtitle}
            </p>
            <ul className="space-y-3 text-base-content-secondary">
              {leftComparison.items.map((item, index) => (
                <li key={index} className="text-muted-foreground">
                  • {item}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Right Card (AI Building - Positive theme) */}
        <Card className="py-0 rounded-lg border border-green-300 bg-green-950/10">
          <CardContent className="p-6 space-y-4">
            <h3 className="text-2xl font-bold flex items-center justify-between text-green-300">
              {rightComparison.title}
              <div className="size-8 rounded-full bg-green-300 flex items-center justify-center">
                <Check className="size-5 text-green-900" strokeWidth={3} />
              </div>
            </h3>
            <p className="text-sm text-green-300 mb-4">
              {rightComparison.subtitle}
            </p>
            <ul className="space-y-3 text-base-content-secondary">
              {rightComparison.items.map((item, index) => (
                <li key={index} className="text-muted-foreground">
                  • {item}
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
