"use client";

import { useState, useEffect } from "react";

interface TimeLeft {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  milliseconds: number;
}

interface TimeUnit {
  value: number;
  label: string;
  max: number;
  progress: number;
}

interface CountdownTimerProps {
  title: string;
  description: string;
  endDays: number;
}

export default function CountdownTimer({
  title,
  description,
  endDays,
}: CountdownTimerProps) {
  const [timeLeft, setTimeLeft] = useState<TimeLeft>({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    milliseconds: 0,
  });

  const STORAGE_KEY = "countdown_timer_end_time";
  const UPDATE_INTERVAL = 10; // Update every 10ms for smooth animation

  const convertMillisecondsToTime = (totalMilliseconds: number): TimeLeft => {
    if (totalMilliseconds <= 0) {
      return { days: 0, hours: 0, minutes: 0, seconds: 0, milliseconds: 0 };
    }

    const days = Math.floor(totalMilliseconds / (24 * 60 * 60 * 1000));
    const hours = Math.floor(
      (totalMilliseconds % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000)
    );
    const minutes = Math.floor(
      (totalMilliseconds % (60 * 60 * 1000)) / (60 * 1000)
    );
    const seconds = Math.floor((totalMilliseconds % (60 * 1000)) / 1000);
    const milliseconds = Math.floor((totalMilliseconds % 1000) / 10); // Show centiseconds (0-99)

    return { days, hours, minutes, seconds, milliseconds };
  };

  const getEndTime = (): number => {
    if (typeof window === "undefined") {
      return Date.now() + endDays * 24 * 60 * 60 * 1000;
    }

    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      const endTime = parseInt(stored);
      // If countdown ended, create a new countdown
      if (endTime <= Date.now()) {
        const newEndTime = Date.now() + endDays * 24 * 60 * 60 * 1000;
        localStorage.setItem(STORAGE_KEY, newEndTime.toString());
        return newEndTime;
      }
      return endTime;
    }

    // First time, set end time
    const newEndTime = Date.now() + endDays * 24 * 60 * 60 * 1000;
    localStorage.setItem(STORAGE_KEY, newEndTime.toString());
    return newEndTime;
  };

  const updateCountdown = () => {
    const endTime = getEndTime();
    const now = Date.now();
    const remaining = Math.max(0, endTime - now);

    if (remaining === 0) {
      // Reset countdown when it reaches zero
      const newEndTime = Date.now() + endDays * 24 * 60 * 60 * 1000;
      localStorage.setItem(STORAGE_KEY, newEndTime.toString());
      setTimeLeft(convertMillisecondsToTime(endDays * 24 * 60 * 60 * 1000));
    } else {
      setTimeLeft(convertMillisecondsToTime(remaining));
    }
  };

  useEffect(() => {
    // Initialize countdown
    updateCountdown();

    // Set up interval to update countdown
    const interval = setInterval(updateCountdown, UPDATE_INTERVAL);

    return () => clearInterval(interval);
  }, [endDays]);

  // Calculate progress for circular progress bars
  const getTimeUnits = (): TimeUnit[] => {
    return [
      {
        value: timeLeft.days,
        label: "DAYS",
        max: 365,
        progress: (timeLeft.days / 365) * 360,
      },
      {
        value: timeLeft.hours,
        label: "HOURS",
        max: 24,
        progress: (timeLeft.hours / 24) * 360,
      },
      {
        value: timeLeft.minutes,
        label: "MINUTES",
        max: 60,
        progress: (timeLeft.minutes / 60) * 360,
      },
      {
        value: timeLeft.seconds,
        label: "SECONDS",
        max: 60,
        progress: (timeLeft.seconds / 60) * 360,
      },
      {
        value: timeLeft.milliseconds,
        label: "CENTISEC",
        max: 100,
        progress: (timeLeft.milliseconds / 100) * 360,
      },
    ];
  };

  const CircularProgress = ({ unit }: { unit: TimeUnit }) => {
    const radius = 38;
    const circumference = 2 * Math.PI * radius;
    const strokeDasharray = `${
      (unit.progress / 360) * circumference
    } ${circumference}`;

    return (
      <div className="relative flex flex-col items-center">
        <div className="relative w-20 h-20">
          {/* Background circle */}
          <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 100 100">
            <circle
              cx="50"
              cy="50"
              r={radius}
              stroke="currentColor"
              strokeWidth="2.5"
              fill="none"
              className="text-muted-foreground/20"
            />
            {/* Progress circle */}
            <circle
              cx="50"
              cy="50"
              r={radius}
              stroke="currentColor"
              strokeWidth="2.5"
              fill="none"
              strokeDasharray={strokeDasharray}
              strokeLinecap="round"
              className="text-red-500 transition-all duration-75 ease-out drop-shadow-sm"
            />
          </svg>

          {/* Value and Label */}
          <div className="absolute inset-0 flex flex-col items-center justify-center">
            <span className="text-xl font-bold font-mono leading-none">
              {Math.floor(unit.value).toString().padStart(2, "0")}
            </span>
            <span className="text-[9px] font-medium text-muted-foreground/70 mt-0.5 tracking-wider">
              {unit.label}
            </span>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="max-w-2xl mx-auto py-6">
      <div className="relative overflow-hidden rounded-xl bg-card/50 backdrop-blur-sm border border-border/50 p-2 shadow-lg">
        <div className="text-center mb-4">
          <h3 className="text-2xl md:text-3xl font-bold mb-2">
            <span className="bg-gradient-to-r from-red-500 via-orange-500 to-red-600 bg-clip-text text-transparent">
              {title}
            </span>
          </h3>
          <p className="text-muted-foreground font-bold text-red-500">
            {description}
          </p>
        </div>

        {/* Circular progress timers */}
        <div className="flex justify-center items-center gap-4 md:gap-6">
          {getTimeUnits().map((unit) => (
            <CircularProgress key={unit.label} unit={unit} />
          ))}
        </div>
      </div>
    </div>
  );
}
