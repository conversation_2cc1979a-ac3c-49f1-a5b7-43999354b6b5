"use client";

import { Check } from "lucide-react";
import Icon from "@/components/icon";
import { Section as SectionType } from "@/types/blocks/section";
import { useState, useEffect } from "react";

export default function Feature4({ section }: { section: SectionType }) {
  if (section.disabled) {
    return null;
  }

  const [activeTab, setActiveTab] = useState(0);

  // Get main feature items for the grid
  const features = section.items?.slice(0, 8) || [];

  // Get current feature for display
  const currentFeature = features[activeTab] || features[0];

  // Auto-switch functionality
  useEffect(() => {
    if (features.length === 0) return;

    const interval = setInterval(() => {
      setActiveTab((prev) => (prev + 1) % features.length);
    }, 5000); // 5 seconds auto-switch

    return () => clearInterval(interval);
  }, [features.length]);

  // Check if any item has an image
  const hasImages =
    features.some((item) => item.image?.src) || currentFeature?.image?.src;

  return (
    <section id={section.name} className="py-16">
      {/* Header Section */}
      <div className="max-w-3xl mx-auto text-center">
        <div className="max-md:px-8 max-w-3xl">
          {section.label && (
            <p className="text-accent font-medium text-sm font-mono mb-3">
              {section.label}
            </p>
          )}
          {section.title && (
            <h2 className="font-bold text-3xl lg:text-4xl tracking-tight mb-8">
              {section.title}
            </h2>
          )}
          {section.description && (
            <div className="text-base-content/80 leading-relaxed mb-8 lg:text-lg text-muted-foreground">
              {section.description}
            </div>
          )}
        </div>
      </div>

      {/* Features Grid */}
      <div className="text-center">
        <div className="grid grid-cols-4 md:flex justify-center gap-4 md:gap-12 max-md:px-8 max-w-3xl mx-auto mb-8">
          {features.map((feature, index) => (
            <span
              key={index}
              className={`flex flex-col items-center justify-center gap-3 select-none cursor-pointer p-2 duration-100 transition-colors ${
                activeTab === index
                  ? "text-primary"
                  : "text-muted-foreground hover:text-foreground"
              }`}
              onClick={() => setActiveTab(index)}
            >
              <span className="flex items-center justify-center">
                {feature.icon ? (
                  <Icon name={feature.icon} className="w-8 h-8" />
                ) : (
                  <div className="w-8 h-8 rounded-lg bg-muted flex items-center justify-center">
                    <span className="text-xs font-semibold">{index + 1}</span>
                  </div>
                )}
              </span>
              <span className="font-medium text-sm text-center">
                {feature.title}
              </span>
            </span>
          ))}
        </div>

        {/* Detailed Content Section - Only show if there are features */}
        {currentFeature && (
          <div className="bg-muted/50">
            <div
              className={`max-w-3xl mx-auto flex flex-col ${
                hasImages
                  ? "md:flex-row justify-center md:justify-start md:items-center"
                  : "items-center text-center"
              } gap-12`}
            >
              <div
                className={`text-muted-foreground leading-relaxed space-y-4 px-12 md:px-0 py-12 ${
                  hasImages ? "max-w-xl" : "max-w-2xl text-center"
                }`}
              >
                <p className="font-medium text-foreground text-lg text-center">
                  {currentFeature.title}
                </p>

                {/* Current feature description */}
                {currentFeature.description && (
                  <p className="text-muted-foreground text-center whitespace-pre-line">
                    {currentFeature.description}
                  </p>
                )}

                {/* Feature List from current feature */}
                {currentFeature.children && (
                  <ul className="space-y-1 text-left inline-block">
                    {currentFeature.children.map((item, index) => {
                      const isLastItem =
                        index === currentFeature.children!.length - 1;
                      return (
                        <li key={index} className="flex items-center gap-2">
                          <Check
                            className={`w-[18px] h-[18px] inline shrink-0 ${
                              isLastItem ? "text-primary" : ""
                            }`}
                          />
                          <span
                            className={
                              isLastItem ? "text-primary font-medium" : ""
                            }
                          >
                            {item.title}
                          </span>
                        </li>
                      );
                    })}
                  </ul>
                )}

                {/* Providers Integration */}
                {currentFeature.providers &&
                  currentFeature.providers.length > 0 && (
                    <div className="pt-3 flex items-center justify-center gap-2 text-sm font-semibold flex-wrap">
                      {currentFeature.providers.map((provider, index) => (
                        <div
                          key={provider.name}
                          className="flex items-center gap-2"
                        >
                          {index > 0 && (
                            <span className="mx-2 text-muted-foreground text-xs">
                              OR
                            </span>
                          )}
                          <img
                            alt={provider.image.alt}
                            className="w-8 h-8"
                            src={provider.image.src}
                          />
                          <span>
                            with{" "}
                            <a
                              className="link hover:text-primary transition-colors"
                              href={provider.url}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {provider.name}
                            </a>
                          </span>
                        </div>
                      ))}
                    </div>
                  )}

                {currentFeature.note && (
                  <p className="text-muted-foreground text-center font-light text-sm">
                    {currentFeature.note}
                  </p>
                )}

                {/* Technology Icons (fallback for items without providers) */}
                {!currentFeature.providers && currentFeature.image && (
                  <div className="pt-3 flex items-center justify-center gap-2 text-sm font-semibold">
                    <img
                      alt={currentFeature.image.alt}
                      className="w-8 h-8"
                      src={currentFeature.image.src}
                    />
                    <span>
                      with{" "}
                      <a
                        className="link hover:text-primary transition-colors"
                        href="#"
                        target="_blank"
                      >
                        {currentFeature.label || "Technology"}
                      </a>
                    </span>
                  </div>
                )}
              </div>

              {/* Conditional Image Area - Only show if images exist */}
              {hasImages && currentFeature.image?.src && (
                <div className="hidden md:flex aspect-square max-md:w-full md:h-[28rem] bg-muted rounded-lg md:order-first items-center justify-center overflow-hidden">
                  <img
                    src={currentFeature.image.src}
                    alt={currentFeature.image.alt || currentFeature.title}
                    className="w-full h-full object-cover rounded-lg"
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </section>
  );
}
