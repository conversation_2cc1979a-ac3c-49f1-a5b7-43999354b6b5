import { Avatar, AvatarImage } from "@/components/ui/avatar";

interface HappyUsersProps {
  layout?: "default" | "split";
}

export default function HappyUsers({ layout }: HappyUsersProps) {
  const isSplitLayout = layout === "split";

  return (
    <div
      className={`mx-auto mt-8 flex w-fit flex-col items-center gap-2 ${
        isSplitLayout ? "lg:mx-0 lg:items-start" : ""
      }`}
    >
      <span
        className={`mx-4 inline-flex items-center -space-x-2 ${
          isSplitLayout ? "lg:mx-0" : ""
        }`}
      >
        {Array.from({ length: 5 }).map((_, index) => (
          <Avatar className="size-12 border" key={index}>
            <AvatarImage
              src={`/imgs/users/${index + 1}.png`}
              alt="placeholder"
            />
          </Avatar>
        ))}
      </span>
      <p
        className={`font-medium text-muted-foreground ${
          isSplitLayout ? "text-center lg:text-left" : "text-left"
        }`}
      >
        28 builders turned their ideas into reality
      </p>
    </div>
  );
}
