import { But<PERSON>, Image, Announcement } from "@/types/blocks/base";

export interface Announcement {
  title?: string;
  description?: string;
  label?: string;
  url?: string;
  target?: string;
}

export interface PromotionalText {
  enabled?: boolean;
  highlight?: string;
  text?: string;
}

export interface Hero {
  name?: string;
  disabled?: boolean;
  layout?: "default" | "split";
  announcement?: Announcement;
  title?: string;
  highlight_text?: string;
  description?: string;
  buttons?: Button[];
  promotional_text?: PromotionalText;
  image?: Image;
  tip?: string;
  show_happy_users?: boolean;
  show_badge?: boolean;
  countdown_timer?: {
    enabled: boolean;
    title: string;
    description: string;
    end_days: number;
  };
}
