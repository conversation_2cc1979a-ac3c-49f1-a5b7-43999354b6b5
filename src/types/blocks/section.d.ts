import { Image, Button } from "@/types/blocks/base";

interface Provider {
  name: string;
  url: string;
  image: Image;
}

export interface SectionItem {
  title?: string;
  description?: string;
  label?: string;
  icon?: string;
  image?: Image;
  buttons?: Button[];
  url?: string;
  target?: string;
  children?: SectionItem[];
  providers?: Provider[];
  note?: string;
  // Comparison-specific properties
  tagline?: string;
  badge?: string;
  badgeVariant?: "default" | "secondary" | "destructive" | "outline";
  pricing?: string;
  highlight?: boolean;
  features?: {
    [key: string]: boolean | string;
  };
}

export interface Section {
  disabled?: boolean;
  name?: string;
  title?: string;
  description?: string;
  label?: string;
  icon?: string;
  image?: Image;
  buttons?: Button[];
  items?: SectionItem[];
}
